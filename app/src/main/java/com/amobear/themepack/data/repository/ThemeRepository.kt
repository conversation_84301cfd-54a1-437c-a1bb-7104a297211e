package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.data.mapper.toEntity
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.toIconCategory
import com.amobear.themepack.data.model.toIconPack
import com.amobear.themepack.data.model.toThemeCategory
import com.amobear.themepack.data.model.toWidgetCategory
import com.amobear.themepack.data.model.toWidgetPack
import com.amobear.themepack.data.network.ApiResult
import com.amobear.themepack.data.network.ApiService
import com.amobear.themepack.data.network.toApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for the new consolidated API structure with unlock detection
 */
@Singleton
class ThemeRepository @Inject constructor(
    private val apiService: ApiService,
    private val themeAppDatabase: ThemeAppDatabase,
) {

    // ============= STATELESS API FLOWS =============
    // These flows return pure API data without unlock status (always unlock = false)

    val themeCategories: Flow<List<ThemeCategory>> = flow {
        // Always return stateless data from API
        val result = getThemeCategoriesStateless()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    val widgetCategories: Flow<List<WidgetCategory>> = flow {
        val result = getWidgetCategoriesStateless()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    val iconCategories: Flow<List<IconCategory>> = flow {
        val result = getIconCategoriesStateless()
        when (result) {
            is ApiResult.Success -> emit(result.data)
            else -> emit(emptyList())
        }
    }

    // ============= STATELESS API METHODS =============
    // These methods return pure API data without saving to database

    /**
     * Get theme categories from API (stateless - no database save, no unlock status)
     */
    suspend fun getThemeCategoriesStateless(): ApiResult<List<ThemeCategory>> {
        return try {
            val response = apiService.getThemes().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    // Convert API to domain models with generated IDs (stateless)
                    val themeCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toThemeCategory(categoryIndex)
                    }
                    // DO NOT save to database - keep it stateless
                    ApiResult.Success(themeCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get widget categories from API (stateless)
     */
    suspend fun getWidgetCategoriesStateless(): ApiResult<List<WidgetCategory>> {
        return try {
            val response = apiService.getWidgetPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val widgetCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toWidgetCategory(categoryIndex)
                    }
                    ApiResult.Success(widgetCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get icon categories from API (stateless)
     */
    suspend fun getIconCategoriesStateless(): ApiResult<List<IconCategory>> {
        return try {
            val response = apiService.getIconPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val iconCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toIconCategory(categoryIndex)
                    }
                    ApiResult.Success(iconCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    // ============= HYBRID METHODS (API + UNLOCK STATE) =============
    // These methods combine stateless API data with unlock state from database

    /**
     * Get theme categories with unlock state merged from database
     * This method combines stateless API data with unlock state from database
     */
    suspend fun getThemeCategoriesWithUnlockState(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        return try {
            // Get stateless data from API
            val apiResult = getThemeCategoriesStateless()

            when (apiResult) {
                is ApiResult.Success -> {
                    val apiCategories = apiResult.data

                    // Get unlock state from database
                    val unlockedThemes = themeAppDatabase.themeDao().getAllThemes()
                    val unlockedWallpaperPacks = themeAppDatabase.wallpaperDao().getAllWallpaperPacks()
                    val unlockedWidgetPacks = themeAppDatabase.widgetDao().getAllWidgetPacks()
                    val unlockedIconPacks = themeAppDatabase.iconDao().getAllIconPacks()

                    // Merge unlock state with API data
                    val categoriesWithUnlockState = apiCategories.map { category ->
                        val themesWithUnlockState = category.themes.map { theme ->
                            // Find matching unlocked theme by title (since IDs are generated)
                            val unlockedTheme = unlockedThemes.find { it.title == theme.title }

                            val wallpaperPacksWithUnlockState = theme.wallpaperPacks.map { pack ->
                                val unlockedPack = unlockedWallpaperPacks.find { it.title == pack.title }
                                pack.copy(isPurchased = unlockedPack?.isPurchased ?: false)
                            }

                            val widgetPacksWithUnlockState = theme.widgetPacks.map { pack ->
                                val unlockedPack = unlockedWidgetPacks.find { it.title == pack.title }
                                pack.copy(isPurchased = unlockedPack?.isPurchased ?: false)
                            }

                            val iconPacksWithUnlockState = theme.iconPacks.map { pack ->
                                val unlockedPack = unlockedIconPacks.find { it.title == pack.title }
                                pack.copy(
                                    isPurchased = unlockedPack?.isPurchased ?: false,
                                    isUnlocked = unlockedPack?.isPurchased ?: false
                                )
                            }

                            theme.copy(
                                downloadDate = unlockedTheme?.downloadDate,
                                isUnlocked = unlockedTheme?.downloadDate != null,
                                wallpaperPacks = wallpaperPacksWithUnlockState,
                                widgetPacks = widgetPacksWithUnlockState,
                                iconPacks = iconPacksWithUnlockState
                            )
                        }

                        category.copy(themes = themesWithUnlockState)
                    }

                    ApiResult.Success(categoriesWithUnlockState)
                }
                is ApiResult.Error -> apiResult
                is ApiResult.Loading -> apiResult
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get widget categories with unlock state merged from database
     */
    suspend fun getWidgetCategoriesWithUnlockState(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {
        return try {
            val apiResult = getWidgetCategoriesStateless()

            when (apiResult) {
                is ApiResult.Success -> {
                    val apiCategories = apiResult.data
                    val unlockedWidgetPacks = themeAppDatabase.widgetDao().getAllWidgetPacks()

                    val categoriesWithUnlockState = apiCategories.map { category ->
                        val widgetPacksWithUnlockState = category.widgetPacks.map { pack ->
                            val unlockedPack = unlockedWidgetPacks.find { it.title == pack.title }
                            pack.copy(isPurchased = unlockedPack?.isPurchased ?: false)
                        }
                        category.copy(widgetPacks = widgetPacksWithUnlockState)
                    }

                    ApiResult.Success(categoriesWithUnlockState)
                }
                is ApiResult.Error -> apiResult
                is ApiResult.Loading -> apiResult
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get icon categories with unlock state merged from database
     */
    suspend fun getIconCategoriesWithUnlockState(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return try {
            val apiResult = getIconCategoriesStateless()

            when (apiResult) {
                is ApiResult.Success -> {
                    val apiCategories = apiResult.data
                    val unlockedIconPacks = themeAppDatabase.iconDao().getAllIconPacks()

                    val categoriesWithUnlockState = apiCategories.map { category ->
                        val iconPacksWithUnlockState = category.iconPacks.map { pack ->
                            val unlockedPack = unlockedIconPacks.find { it.title == pack.title }
                            pack.copy(
                                isPurchased = unlockedPack?.isPurchased ?: false,
                                isUnlocked = unlockedPack?.isPurchased ?: false
                            )
                        }
                        category.copy(iconPacks = iconPacksWithUnlockState)
                    }

                    ApiResult.Success(categoriesWithUnlockState)
                }
                is ApiResult.Error -> apiResult
                is ApiResult.Loading -> apiResult
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    // ============= LEGACY METHODS (for backward compatibility) =============

    /**
     * Get all theme categories and themes with unlock status (legacy method)
     */
    suspend fun getThemeCategories(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        // For now, just return stateless data
        return getThemeCategoriesStateless()
    }

    /**
     * Get all widget categories and widget packs (legacy method)
     */
    suspend fun getWidgetCategories(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {
        return getWidgetCategoriesStateless()
    }

    /**
     * Get all icon categories and icon packs (legacy method)
     */
    suspend fun getIconCategories(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return getIconCategoriesStateless()
    }

    /**
     * Get theme by ID
     */
    fun getThemeById(themeId: Int): Flow<Theme?> = flow {
        val theme = themeAppDatabase.themeDao().getThemeById(themeId)?.toDomain()
        emit(theme)
    }

    /**
     * Get themes by category ID
     */
    fun getThemesByCategoryId(categoryId: Int): Flow<List<Theme>> =
        themeAppDatabase.themeDao().getThemesByCategory(categoryId).map {
            it.map { it.toDomain() }
        }
}