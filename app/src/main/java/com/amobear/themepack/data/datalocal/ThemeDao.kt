package com.amobear.themepack.data.datalocal

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface ThemeDao {
    @Query("SELECT * FROM themes")
    fun getAllThemes(): Flow<List<ThemeEntity>>

    @Query("SELECT * FROM themes")
    suspend fun getAllThemes(): List<ThemeEntity>

    @Query("SELECT * FROM themes WHERE categoryId = :categoryId")
    fun getThemesByCategory(categoryId: Int): Flow<List<ThemeEntity>>

    @Query("SELECT * FROM themes WHERE id = :themeId")
    suspend fun getThemeById(themeId: Int): ThemeEntity?

    @Query("SELECT * FROM themes WHERE title = :title LIMIT 1")
    suspend fun getThemeByTitle(title: String): ThemeEntity?

    @Query("SELECT * FROM themes WHERE downloadDate IS NOT NULL")
    fun getUnlockedThemes(): Flow<List<ThemeEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTheme(theme: ThemeEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertThemes(themes: List<ThemeEntity>)

    @Update
    suspend fun updateTheme(theme: ThemeEntity)

    @Delete
    suspend fun deleteTheme(theme: ThemeEntity)

    @Query("DELETE FROM themes")
    suspend fun deleteAllThemes()
}

@Dao
interface ThemeCategoryDao {
    @Query("SELECT * FROM theme_categories ORDER BY weight ASC")
    fun getAllCategories(): Flow<List<ThemeCategoryEntity>>

    @Query("SELECT * FROM theme_categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: Int): ThemeCategoryEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategory(category: ThemeCategoryEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<ThemeCategoryEntity>)

    @Update
    suspend fun updateCategory(category: ThemeCategoryEntity)

    @Delete
    suspend fun deleteCategory(category: ThemeCategoryEntity)

    @Query("DELETE FROM theme_categories")
    suspend fun deleteAllCategories()
}

@Dao
interface WallpaperDao {
    @Query("SELECT * FROM wallpaper_packs WHERE themeId = :themeId")
    fun getWallpaperPacksByTheme(themeId: Int): Flow<List<WallpaperPackEntity>>

    @Query("SELECT * FROM wallpapers WHERE packId = :packId")
    fun getWallpapersByPack(packId: Int): Flow<List<WallpaperEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWallpaperPacks(packs: List<WallpaperPackEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWallpapers(wallpapers: List<WallpaperEntity>)
}

@Dao
interface WidgetPackDao {
    @Query("SELECT * FROM widget_packs WHERE id = :packId")
    suspend fun getWidgetPackById(packId: Int): WidgetPackEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgetPacks(packs: List<WidgetPackEntity>)
}

@Dao
interface WidgetCategoryDao {
    @Query("SELECT * FROM widget_categories ORDER BY weight ASC")
    fun getAllWidgetCategories(): Flow<List<WidgetCategoryEntity>>

    @Query("SELECT * FROM widget_categories WHERE id = :categoryId")
    suspend fun getWidgetCategoryById(categoryId: Int): WidgetCategoryEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgetCategory(category: WidgetCategoryEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgetCategories(categories: List<WidgetCategoryEntity>)

    @Update
    suspend fun updateWidgetCategory(category: WidgetCategoryEntity)

    @Delete
    suspend fun deleteWidgetCategory(category: WidgetCategoryEntity)

    @Query("DELETE FROM widget_categories")
    suspend fun deleteAllWidgetCategories()
}

@Dao
interface WidgetDao {
    @Query("SELECT * FROM widgets WHERE packId = :packId")
    fun getWidgetsByPack(packId: Int): Flow<List<WidgetEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgetCategories(categories: List<WidgetCategoryEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgetPacks(packs: List<WidgetPackEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWidgets(widgets: List<WidgetEntity>)
}

@Dao
interface IconCategoryDao {
    @Query("SELECT * FROM icon_categories ORDER BY weight ASC")
    fun getAllIconCategories(): Flow<List<IconCategoryEntity>>

    @Query("SELECT * FROM icon_categories WHERE id = :categoryId")
    suspend fun getIconCategoryById(categoryId: Int): IconCategoryEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIconCategory(category: IconCategoryEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIconCategories(categories: List<IconCategoryEntity>)

    @Update
    suspend fun updateIconCategory(category: IconCategoryEntity)

    @Delete
    suspend fun deleteIconCategory(category: IconCategoryEntity)

    @Query("DELETE FROM icon_categories")
    suspend fun deleteAllIconCategories()

}

@Dao
interface IconPackDao {

    @Query("SELECT * FROM icon_packs WHERE id = :packId")
    suspend fun getIconPackById(packId: Int): IconPackEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIconPacks(packs: List<IconPackEntity>)
}

@Dao
interface IconDao {

    @Query("SELECT * FROM icon_categories ORDER BY weight ASC")
    fun getAllIconCategories(): Flow<List<IconCategoryEntity>>

    @Query("SELECT * FROM icon_packs WHERE categoryId = :categoryId")
    fun getIconPacksByCategory(categoryId: Int): Flow<List<IconPackEntity>>

    @Query("SELECT * FROM icons WHERE iconPackId = :packId")
    fun getIconsByPack(packId: Int): Flow<List<IconEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIconCategories(categories: List<IconCategoryEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIconPacks(packs: List<IconPackEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIcons(icons: List<IconEntity>)
}
