package com.amobear.themepack.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import com.squareup.moshi.JsonClass

/**
 * API Response wrapper
 */
@JsonClass(generateAdapter = true)
data class ApiResponse<T>(
    val success: Boolean,
    val data: T,
    val error: ApiError? = null
)

@JsonClass(generateAdapter = true)
data class ApiError(
    val code: String,
    val message: String
)

// ============= THEMES API MODELS =============

/**
 * Response for GET /api/themes endpoint
 */
@JsonClass(generateAdapter = true)
data class ThemesApiResponse(
    val categories: List<ApiThemeCategory>
)

@JsonClass(generateAdapter = true)
data class ApiThemeCategory(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val themes: List<ApiTheme>
)

@JsonClass(generateAdapter = true)
data class ApiTheme(
    val title: String,
    val description: String,
    val previewImage: String,
    val wallpaperPacks: List<ApiWallpaperPack>,
    val widgetPacks: List<ApiWidgetPack>,
    val iconPacks: List<ApiIconPack>
)

@JsonClass(generateAdapter = true)
data class ApiWallpaperPack(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val wallpapers: List<ApiWallpaper>
)

@JsonClass(generateAdapter = true)
data class ApiWallpaper(
    val title: String,
    val imageUrl: String,
    val description: String
)

// ============= WIDGET PACKS API MODELS =============

/**
 * Response for GET /api/widget-packs endpoint
 */
@JsonClass(generateAdapter = true)
data class WidgetPacksApiResponse(
    val categories: List<ApiWidgetCategory>
)

@JsonClass(generateAdapter = true)
data class ApiWidgetCategory(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val widgetPacks: List<ApiWidgetPack>
)

@JsonClass(generateAdapter = true)
data class ApiWidgetPack(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val type: String, // "clock", "weather", "calendar", "frame"
    val widgets: List<ApiWidget>,
    val compatibleThemes: List<ApiCompatibleTheme> = emptyList()
)

@JsonClass(generateAdapter = true)
data class ApiWidget(
    val title: String,
    val previewImage: String,
    val width: Int,
    val height: Int,
    val size: String // "small", "medium", "large"
)

@JsonClass(generateAdapter = true)
data class ApiCompatibleTheme(
    val title: String
)

// ============= ICON PACKS API MODELS =============

/**
 * Response for GET /api/icon-packs endpoint
 */
@JsonClass(generateAdapter = true)
data class IconPacksApiResponse(
    val categories: List<ApiIconCategory>
)

@JsonClass(generateAdapter = true)
data class ApiIconCategory(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val iconPacks: List<ApiIconPack>
)

@JsonClass(generateAdapter = true)
data class ApiIconPack(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val icons: List<ApiIcon>,
    val compatibleThemes: List<ApiCompatibleTheme> = emptyList()
)

@JsonClass(generateAdapter = true)
data class ApiIcon(
    val appId: String,
    val name: String,
    val imageUrl: String
)

// ============= MAPPING EXTENSIONS =============

/**
 * Extension functions to convert API models to domain models
 */

fun ApiThemeCategory.toThemeCategory(categoryIndex: Int): ThemeCategory {
    val categoryId = generateCategoryId(title, categoryIndex)
    return ThemeCategory(
        id = categoryId,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        themes = themes.mapIndexed { themeIndex, theme ->
            theme.toTheme(categoryId, themeIndex)
        }
    )
}

fun ApiTheme.toTheme(categoryId: Int, themeIndex: Int): Theme {
    val themeId = generateThemeId(categoryId, title, themeIndex)
    return Theme(
        id = themeId,
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        wallpaperPacks = wallpaperPacks.mapIndexed { packIndex, pack ->
            pack.toWallpaperPack(themeId, packIndex)
        },
        widgetPacks = widgetPacks.mapIndexed { packIndex, pack ->
            pack.toWidgetPack(themeId, packIndex)
        },
        iconPacks = iconPacks.mapIndexed { packIndex, pack ->
            pack.toIconPack(themeId, packIndex)
        }
    )
}

fun ApiWallpaperPack.toWallpaperPack(themeId: Int, packIndex: Int): WallpaperPack {
    val packId = generateWallpaperPackId(themeId, title, packIndex)
    return WallpaperPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        wallpapers = wallpapers.mapIndexed { wallpaperIndex, wallpaper ->
            wallpaper.toWallpaper(packId, wallpaperIndex)
        },
        isPurchased = false
    )
}

fun ApiWallpaper.toWallpaper(packId: Int, wallpaperIndex: Int): Wallpaper {
    val wallpaperId = generateWallpaperId(packId, title, wallpaperIndex)
    return Wallpaper(
        id = wallpaperId,
        title = title,
        imageUrl = imageUrl,
        description = description,
        packId = packId,
        isUnlocked = false,
        localPath = null
    )
}

fun ApiWidgetPack.toWidgetPack(themeId: Int, packIndex: Int): WidgetPack {
    val packId = generateWidgetPackId(themeId, title, packIndex)
    return WidgetPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = themeId, // Using themeId as categoryId for widgets in themes
        type = WidgetType.fromString(type),
        widgets = widgets.mapIndexed { widgetIndex, widget ->
            widget.toWidget(packId, widgetIndex)
        },
        compatibleThemes = compatibleThemes.mapIndexed { themeIndex, theme ->
            theme.toCompatibleTheme(themeIndex)
        },
        isPurchased = false
    )
}

fun ApiWidget.toWidget(packId: Int, widgetIndex: Int): Widget {
    val widgetId = generateWidgetId(packId, title, widgetIndex)
    return Widget(
        id = widgetId,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = WidgetSize.fromString(size),
        packId = packId
    )
}

fun ApiIconPack.toIconPack(themeId: Int, packIndex: Int): IconPack {
    val packId = generateIconPackId(themeId, title, packIndex)
    return IconPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = themeId, // Using themeId as categoryId for icons in themes
        icons = icons.mapIndexed { iconIndex, icon ->
            icon.toIcon(packId, iconIndex)
        },
        compatibleThemes = compatibleThemes.mapIndexed { themeIndex, theme ->
            theme.toCompatibleTheme(themeIndex)
        },
        isPurchased = false,
        isUnlocked = false,
        localPath = null
    )
}

fun ApiIcon.toIcon(packId: Int, iconIndex: Int): Icon {
    val iconId = generateIconId(packId, appId, iconIndex)
    return Icon(
        id = iconId,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        iconPackId = packId,
        localPath = null
    )
}

fun ApiCompatibleTheme.toCompatibleTheme(themeIndex: Int): CompatibleTheme {
    val themeId = generateCompatibleThemeId(title, themeIndex)
    return CompatibleTheme(
        id = themeId,
        title = title
    )
}

fun ApiWidgetCategory.toWidgetCategory(categoryIndex: Int): WidgetCategory {
    val categoryId = generateCategoryId(title, categoryIndex)
    return WidgetCategory(
        id = categoryId,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        widgetPacks = widgetPacks.mapIndexed { packIndex, pack ->
            pack.toWidgetPack(categoryId, packIndex)
        }
    )
}

fun ApiIconCategory.toIconCategory(categoryIndex: Int): IconCategory {
    val categoryId = generateCategoryId(title, categoryIndex)
    return IconCategory(
        id = categoryId,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        iconPacks = iconPacks.mapIndexed { packIndex, pack ->
            pack.toIconPack(categoryId, packIndex)
        }
    )
}

// ============= ID GENERATION FUNCTIONS =============

/**
 * Generate stable, unique IDs using hash codes of composite keys
 * These IDs will be consistent across app sessions for the same data
 */

private fun generateCategoryId(title: String, index: Int): Int {
    return "category_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateThemeId(categoryId: Int, title: String, index: Int): Int {
    return "theme_${categoryId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateWallpaperPackId(themeId: Int, title: String, index: Int): Int {
    return "wallpaper_pack_${themeId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateWallpaperId(packId: Int, title: String, index: Int): Int {
    return "wallpaper_${packId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateWidgetPackId(themeId: Int, title: String, index: Int): Int {
    return "widget_pack_${themeId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateWidgetId(packId: Int, title: String, index: Int): Int {
    return "widget_${packId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateIconPackId(themeId: Int, title: String, index: Int): Int {
    return "icon_pack_${themeId}_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateIconId(packId: Int, appId: String, index: Int): Int {
    return "icon_${packId}_${appId}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}

private fun generateCompatibleThemeId(title: String, index: Int): Int {
    return "compatible_theme_${title}_$index".hashCode().let {
        if (it < 0) -it else it
    }
}