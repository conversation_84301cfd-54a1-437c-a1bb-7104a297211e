package com.amobear.themepack.presentation.home.screens.browse

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.network.ApiResult
import com.amobear.themepack.data.repository.ThemeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for browsing all available content with unlock status
 * 
 * Architecture:
 * - Uses API data for all available content
 * - Shows real-time unlock status via image-based detection
 * - Does NOT use database (that's only for ProfileScreen)
 * - Perfect for browse, suggestions, home screens
 */
@HiltViewModel
class BrowseViewModel @Inject constructor(
    private val themeRepository: ThemeRepository
) : ViewModel() {

    private val _state = MutableStateFlow(BrowseState())
    val state = _state.asStateFlow()

    init {
        loadAllContent()
    }

    /**
     * Load all available content with unlock status
     * Uses API + image-based unlock detection (not database)
     */
    private fun loadAllContent() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true)

            try {
                // Load themes with unlock status
                val themesResult = themeRepository.getThemeCategoriesWithUnlockStatus()
                val widgetsResult = themeRepository.getWidgetCategoriesWithUnlockStatus()
                val iconsResult = themeRepository.getIconCategoriesWithUnlockStatus()

                _state.value = _state.value.copy(
                    themeCategories = if (themesResult is ApiResult.Success) themesResult.data else emptyList(),
                    widgetCategories = if (widgetsResult is ApiResult.Success) widgetsResult.data else emptyList(),
                    iconCategories = if (iconsResult is ApiResult.Success) iconsResult.data else emptyList(),
                    isLoading = false
                )
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    isLoading = false,
                    errorMessage = e.message
                )
            }
        }
    }

    /**
     * Purchase content and refresh to show updated unlock status
     */
    fun purchaseContent(
        contentType: ContentType,
        title: String,
        previewImage: String,
        price: Int,
        description: String = "",
        weight: Int = 0
    ) {
        viewModelScope.launch {
            try {
                val success = when (contentType) {
                    ContentType.THEME -> {
                        themeRepository.purchaseTheme(
                            title = title,
                            description = description,
                            previewImage = previewImage
                        )
                    }
                    ContentType.WALLPAPER_PACK -> {
                        themeRepository.purchaseWallpaperPack(
                            title = title,
                            previewImage = previewImage,
                            weight = weight,
                            coin = price
                        )
                    }
                    ContentType.WIDGET_PACK -> {
                        themeRepository.purchaseWidgetPack(
                            title = title,
                            previewImage = previewImage,
                            weight = weight,
                            coin = price,
                            type = com.amobear.themepack.data.model.WidgetType.CLOCK
                        )
                    }
                    ContentType.ICON_PACK -> {
                        themeRepository.purchaseIconPack(
                            title = title,
                            previewImage = previewImage,
                            weight = weight,
                            coin = price
                        )
                    }
                }

                if (success) {
                    // Refresh content to show updated unlock status
                    loadAllContent()
                } else {
                    _state.value = _state.value.copy(
                        errorMessage = "Purchase failed"
                    )
                }
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    errorMessage = "Purchase error: ${e.message}"
                )
            }
        }
    }

    /**
     * Refresh all content
     */
    fun refresh() {
        loadAllContent()
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _state.value = _state.value.copy(errorMessage = null)
    }
}

/**
 * State for browse screen
 */
data class BrowseState(
    val themeCategories: List<ThemeCategory> = emptyList(),
    val widgetCategories: List<WidgetCategory> = emptyList(),
    val iconCategories: List<IconCategory> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Content types for purchase operations
 */
enum class ContentType {
    THEME,
    WALLPAPER_PACK,
    WIDGET_PACK,
    ICON_PACK
}
